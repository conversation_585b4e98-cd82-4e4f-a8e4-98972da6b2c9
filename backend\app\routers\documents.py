from fastapi import APIRouter, HTTPException, Depends, Form
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import json
from ..services.document_service import document_service
from ..routers.auth import get_current_user

router = APIRouter()

class DocumentGenerateRequest(BaseModel):
    template_id: str
    form_data: Dict[str, Any]
    signature_data: Optional[str] = None

class DocumentResponse(BaseModel):
    id: str
    docx_available: bool
    pdf_available: bool
    created_at: str
    docx_filename: str
    pdf_filename: Optional[str] = None

@router.post("/generate", response_model=DocumentResponse)
async def generate_document(
    request: DocumentGenerateRequest,
    current_user: dict = Depends(get_current_user)
):
    """Generate .docx and .pdf documents from template and form data"""
    return await document_service.generate_document(
        template_id=request.template_id,
        form_data=request.form_data,
        signature_data=request.signature_data,
        user_id=current_user["id"]
    )

@router.get("/{document_id}/download/{file_type}")
async def download_document(
    document_id: str,
    file_type: str,
    current_user: dict = Depends(get_current_user)
):
    """Download generated document (docx or pdf)"""
    if file_type not in ["docx", "pdf"]:
        raise HTTPException(status_code=400, detail="File type must be 'docx' or 'pdf'")
    
    file_path = await document_service.get_document_file(document_id, file_type, current_user["id"])
    
    # Set appropriate media type
    media_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document" if file_type == "docx" else "application/pdf"
    
    return FileResponse(
        path=str(file_path),
        media_type=media_type,
        filename=file_path.name
    )

@router.get("/", response_model=List[Dict[str, Any]])
async def get_documents(current_user: dict = Depends(get_current_user)):
    """Get all documents for the current user"""
    return await document_service.get_user_documents(current_user["id"])
