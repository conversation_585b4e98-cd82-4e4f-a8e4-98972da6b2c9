# Deployment Guide for Doc Maker

This guide covers deploying the Doc Maker application to various cloud platforms with Supabase integration.

## Prerequisites

1. **Supabase Project Setup**
   - Create a Supabase project at [supabase.com](https://supabase.com)
   - Run the database schema from `database/schema.sql`
   - Create a storage bucket named `documents`
   - Note down your project URL, anon key, and service role key

2. **Environment Variables**
   - Backend: Copy `backend/.env.example` to `backend/.env` and fill in values
   - Frontend: Copy `frontend/.env.example` to `frontend/.env` and fill in values

## Frontend Deployment (Vercel)

### Option 1: Vercel CLI
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy from project root
cd frontend
vercel

# Set environment variables
vercel env add REACT_APP_SUPABASE_URL
vercel env add REACT_APP_SUPABASE_ANON_KEY
vercel env add REACT_APP_API_URL
vercel env add REACT_APP_ENVIRONMENT
vercel env add REACT_APP_APP_NAME
vercel env add REACT_APP_MAX_FILE_SIZE
vercel env add REACT_APP_ALLOWED_FILE_TYPES
vercel env add REACT_APP_STORAGE_BUCKET
```

### Option 2: Vercel Dashboard
1. Connect your GitHub repository to Vercel
2. Set the root directory to `frontend`
3. Add environment variables in Project Settings > Environment Variables:

| Variable | Value |
|----------|-------|
| `REACT_APP_SUPABASE_URL` | `https://uzxqnxvwrjsuvkblsudy.supabase.co` |
| `REACT_APP_SUPABASE_ANON_KEY` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `REACT_APP_API_URL` | `https://your-backend-domain.com` |
| `REACT_APP_ENVIRONMENT` | `production` |
| `REACT_APP_APP_NAME` | `Doc Maker` |
| `REACT_APP_MAX_FILE_SIZE` | `10485760` |
| `REACT_APP_ALLOWED_FILE_TYPES` | `.docx,.doc` |
| `REACT_APP_STORAGE_BUCKET` | `documents` |

### Option 3: Netlify
1. Connect your repository to Netlify
2. Set build directory to `frontend`
3. Build command: `npm run build`
4. Publish directory: `build`
5. Add the same environment variables as above

## Backend Deployment

### Option 1: Railway

1. **Connect Repository**
   - Go to [railway.app](https://railway.app)
   - Create new project from GitHub repo
   - Select your repository

2. **Configure Build**
   - Railway will auto-detect Python
   - Root directory: `backend`
   - Start command: `uvicorn app.main:app --host 0.0.0.0 --port $PORT`

3. **Environment Variables**
   Add these in Railway dashboard > Variables:

| Variable | Value |
|----------|-------|
| `SUPABASE_URL` | `https://uzxqnxvwrjsuvkblsudy.supabase.co` |
| `SUPABASE_KEY` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (Service Role Key) |
| `SECRET_KEY` | Generate with `openssl rand -hex 32` |
| `ENVIRONMENT` | `production` |
| `DEBUG` | `false` |
| `STORAGE_BUCKET` | `documents` |
| `MAX_FILE_SIZE` | `10485760` |
| `ALLOWED_ORIGINS` | `https://your-frontend-domain.vercel.app` |

### Option 2: Render

1. **Create Web Service**
   - Go to [render.com](https://render.com)
   - Create new Web Service from Git
   - Connect your repository

2. **Configure Service**
   - Name: `doc-maker-backend`
   - Environment: `Python 3`
   - Build Command: `cd backend && pip install -r requirements.txt`
   - Start Command: `cd backend && uvicorn app.main:app --host 0.0.0.0 --port $PORT`

3. **Environment Variables**
   Add the same variables as Railway above

### Option 3: Heroku

1. **Create App**
```bash
# Install Heroku CLI
heroku create doc-maker-backend

# Set buildpack
heroku buildpacks:set heroku/python

# Set environment variables
heroku config:set SUPABASE_URL=https://uzxqnxvwrjsuvkblsudy.supabase.co
heroku config:set SUPABASE_KEY=your_service_role_key
heroku config:set SECRET_KEY=your_secret_key
heroku config:set ENVIRONMENT=production
heroku config:set DEBUG=false
heroku config:set STORAGE_BUCKET=documents
heroku config:set MAX_FILE_SIZE=10485760
heroku config:set ALLOWED_ORIGINS=https://your-frontend-domain.vercel.app

# Deploy
git subtree push --prefix backend heroku main
```

2. **Create Procfile** (in backend directory):
```
web: uvicorn app.main:app --host 0.0.0.0 --port $PORT
```

## Supabase Storage Setup

1. **Create Storage Bucket**
```sql
-- Run in Supabase SQL Editor
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', false);
```

2. **Set Storage Policies**
```sql
-- Allow authenticated users to upload files
CREATE POLICY "Users can upload own files" ON storage.objects
FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to view own files
CREATE POLICY "Users can view own files" ON storage.objects
FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to delete own files
CREATE POLICY "Users can delete own files" ON storage.objects
FOR DELETE USING (auth.uid()::text = (storage.foldername(name))[1]);
```

## Post-Deployment Checklist

- [ ] Frontend deployed and accessible
- [ ] Backend deployed and accessible
- [ ] Database schema applied
- [ ] Storage bucket created with proper policies
- [ ] Environment variables set correctly
- [ ] CORS configured for frontend domain
- [ ] SSL certificates active
- [ ] Health checks passing
- [ ] File upload/download working
- [ ] Authentication flow working

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Update `ALLOWED_ORIGINS` in backend environment variables
   - Include your frontend domain

2. **File Upload Failures**
   - Check storage bucket exists
   - Verify storage policies are set
   - Ensure service role key is correct

3. **Authentication Issues**
   - Verify Supabase keys are correct
   - Check JWT secret key is set
   - Ensure RLS policies are properly configured

4. **Build Failures**
   - Check all dependencies are in requirements.txt/package.json
   - Verify Python/Node versions are compatible
   - Check for missing environment variables

### Monitoring

- **Frontend**: Use Vercel Analytics or Netlify Analytics
- **Backend**: Use Railway metrics or Render monitoring
- **Database**: Use Supabase dashboard for query performance
- **Storage**: Monitor storage usage in Supabase dashboard

## Security Considerations

1. **Never expose service role key in frontend**
2. **Use HTTPS in production**
3. **Set proper CORS origins**
4. **Enable RLS on all tables**
5. **Regularly rotate JWT secret keys**
6. **Monitor for unusual API usage**
7. **Set up proper backup strategies**
