import axios from 'axios'
import { getSession } from './supabase'

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
})

// Add auth token to requests
api.interceptors.request.use(async (config) => {
  const session = await getSession()
  if (session?.access_token) {
    config.headers.Authorization = `Bearer ${session.access_token}`
  }
  return config
})

// Templates API
export const templatesApi = {
  upload: async (file) => {
    const formData = new FormData()
    formData.append('file', file)
    const response = await api.post('/api/templates/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
    return response.data
  },

  getAll: async () => {
    const response = await api.get('/api/templates/')
    return response.data
  },

  getPlaceholders: async (templateId) => {
    const response = await api.get(`/api/templates/${templateId}/placeholders`)
    return response.data
  },
}

// Documents API
export const documentsApi = {
  generate: async (templateId, formData, signatureData = null) => {
    const response = await api.post('/api/documents/generate', {
      template_id: templateId,
      form_data: formData,
      signature_data: signatureData,
    })
    return response.data
  },

  download: async (documentId, fileType) => {
    const response = await api.get(`/api/documents/${documentId}/download/${fileType}`, {
      responseType: 'blob',
    })
    return response
  },

  getAll: async () => {
    const response = await api.get('/api/documents/')
    return response.data
  },
}

// Auth API
export const authApi = {
  register: async (email, password) => {
    const response = await api.post('/api/auth/register', {
      email,
      password,
    })
    return response.data
  },

  login: async (email, password) => {
    const response = await api.post('/api/auth/login', {
      email,
      password,
    })
    return response.data
  },

  getMe: async () => {
    const response = await api.get('/api/auth/me')
    return response.data
  },
}

export default api
