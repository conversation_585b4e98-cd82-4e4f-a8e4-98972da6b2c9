# Doc Maker - Full-Stack Document Generation App

A comprehensive web and mobile-ready application for generating formatted .docx and .pdf reports from templates with digital signature support.

## Features

- 📄 Upload .docx templates with placeholders ({{TrainNo}}, {{LocoNo}}, etc.)
- 📝 Dynamic form generation based on template placeholders
- ✍️ Digital signature capture using HTML5 canvas
- 📋 Generate .docx and .pdf reports with preserved formatting
- ☁️ Cloud storage integration with Supabase
- 🔐 User authentication and authorization
- 📱 Mobile-responsive design

## Tech Stack

### Backend
- **Python FastAPI** - High-performance API framework
- **docxtpl** - .docx template processing
- **python-docx2pdf** - PDF conversion
- **Supabase** - Database and authentication
- **Pillow** - Image processing for signatures

### Frontend
- **React** - UI framework
- **Tailwind CSS** - Styling
- **React Router** - Navigation
- **Supabase JS** - Authentication and database client
- **HTML5 Canvas** - Signature capture

## Project Structure

```
doc-maker/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── models/
│   │   ├── routers/
│   │   ├── services/
│   │   └── utils/
│   ├── templates/
│   ├── generated/
│   ├── requirements.txt
│   └── .env
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── services/
│   │   └── utils/
│   ├── public/
│   ├── package.json
│   └── .env
└── README.md
```

## Quick Start

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## API Endpoints

### Templates
- `POST /api/templates/upload` - Upload .docx template
- `GET /api/templates` - List user templates
- `GET /api/templates/{id}/placeholders` - Get template placeholders

### Documents
- `POST /api/documents/generate` - Generate document from template
- `GET /api/documents/{id}/download` - Download generated document
- `POST /api/signatures/upload` - Upload signature image

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user

## Environment Variables

### Backend (.env)
```
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
SECRET_KEY=your_secret_key
```

### Frontend (.env)
```
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_API_URL=http://localhost:8000
```
