import os
import uuid
import base64
from pathlib import Path
from typing import Dict, Any, Optional
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from docxtpl import DocxTemplate, InlineImage
from docx.shared import Inches
from PIL import Image
import io
from datetime import datetime
import json

# Import for PDF conversion (Windows-specific)
try:
    from docx2pdf import convert
    PDF_CONVERSION_AVAILABLE = True
except ImportError:
    PDF_CONVERSION_AVAILABLE = False

from ..utils.supabase_client import get_supabase_client
from .template_service import template_service

class DocumentService:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.generated_dir = Path("generated")
        self.signatures_dir = Path("signatures")
        self.generated_dir.mkdir(exist_ok=True)
        self.signatures_dir.mkdir(exist_ok=True)
    
    async def generate_document(
        self, 
        template_id: str, 
        form_data: Dict[str, Any], 
        signature_data: Optional[str], 
        user_id: str
    ) -> Dict[str, Any]:
        """Generate .docx and .pdf documents from template and form data"""
        
        # Get template file path
        template_path = template_service.get_template_path(template_id, user_id)
        
        # Process signature if provided
        signature_path = None
        if signature_data:
            signature_path = await self._process_signature(signature_data)
        
        # Generate unique document ID
        document_id = str(uuid.uuid4())
        
        # Generate .docx file
        docx_filename = f"{document_id}.docx"
        docx_path = self.generated_dir / docx_filename
        
        try:
            # Load template
            doc = DocxTemplate(str(template_path))
            
            # Prepare context with form data
            context = form_data.copy()
            
            # Add signature image if available
            if signature_path and signature_path.exists():
                # Create inline image for signature
                signature_image = InlineImage(doc, str(signature_path), width=Inches(2))
                context['signature'] = signature_image
                
                # Also add a signature placeholder for supervisor area
                context['supervisor_signature'] = signature_image
            
            # Render document
            doc.render(context)
            doc.save(str(docx_path))
            
            # Generate PDF if conversion is available
            pdf_path = None
            pdf_filename = None
            if PDF_CONVERSION_AVAILABLE:
                pdf_filename = f"{document_id}.pdf"
                pdf_path = self.generated_dir / pdf_filename
                try:
                    convert(str(docx_path), str(pdf_path))
                except Exception as e:
                    print(f"PDF conversion failed: {e}")
                    pdf_path = None
            
            # Save document metadata to database
            document_data = {
                "id": document_id,
                "user_id": user_id,
                "template_id": template_id,
                "form_data": json.dumps(form_data),
                "docx_filename": docx_filename,
                "pdf_filename": pdf_filename,
                "has_signature": signature_data is not None,
                "created_at": datetime.now().isoformat()
            }
            
            result = self.supabase.table("documents").insert(document_data).execute()
            
            return {
                "id": document_id,
                "docx_available": True,
                "pdf_available": pdf_path is not None and pdf_path.exists(),
                "created_at": document_data["created_at"],
                "docx_filename": docx_filename,
                "pdf_filename": pdf_filename
            }
            
        except Exception as e:
            # Clean up files on error
            if docx_path.exists():
                docx_path.unlink()
            if signature_path and signature_path.exists():
                signature_path.unlink()
            raise HTTPException(status_code=500, detail=f"Failed to generate document: {str(e)}")
    
    async def _process_signature(self, signature_data: str) -> Path:
        """Process base64 signature data and save as PNG"""
        try:
            # Remove data URL prefix if present
            if signature_data.startswith('data:image/'):
                signature_data = signature_data.split(',')[1]
            
            # Decode base64 data
            image_data = base64.b64decode(signature_data)
            
            # Open with PIL and convert to PNG
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGBA if not already
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Make white pixels transparent
            data = image.getdata()
            new_data = []
            for item in data:
                # Change all white (also shades of whites) pixels to transparent
                if item[0] > 240 and item[1] > 240 and item[2] > 240:
                    new_data.append((255, 255, 255, 0))
                else:
                    new_data.append(item)
            
            image.putdata(new_data)
            
            # Save signature
            signature_id = str(uuid.uuid4())
            signature_filename = f"{signature_id}.png"
            signature_path = self.signatures_dir / signature_filename
            
            image.save(str(signature_path), "PNG")
            
            return signature_path
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid signature data: {str(e)}")
    
    async def get_document_file(self, document_id: str, file_type: str, user_id: str) -> Path:
        """Get document file path for download"""
        try:
            # Get document metadata
            result = self.supabase.table("documents").select("*").eq("id", document_id).eq("user_id", user_id).execute()
            
            if not result.data:
                raise HTTPException(status_code=404, detail="Document not found")
            
            document = result.data[0]
            
            if file_type == "docx":
                filename = document["docx_filename"]
            elif file_type == "pdf":
                filename = document["pdf_filename"]
                if not filename:
                    raise HTTPException(status_code=404, detail="PDF version not available")
            else:
                raise HTTPException(status_code=400, detail="Invalid file type")
            
            file_path = self.generated_dir / filename
            
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="File not found")
            
            return file_path
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get document: {str(e)}")
    
    async def get_user_documents(self, user_id: str) -> list:
        """Get all documents for a user"""
        try:
            result = self.supabase.table("documents").select("*").eq("user_id", user_id).order("created_at", desc=True).execute()
            return result.data if result.data else []
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to fetch documents: {str(e)}")

document_service = DocumentService()
