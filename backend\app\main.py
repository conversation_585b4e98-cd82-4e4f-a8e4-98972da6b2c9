from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import os
import uuid
import shutil
from pathlib import Path
from typing import List, Optional
import json
import re
from datetime import datetime
import base64
from io import BytesIO
from PIL import Image

from .routers import templates, documents, auth
from .services.template_service import TemplateService
from .services.document_service import DocumentService
from .services.auth_service import AuthService
from .utils.supabase_client import get_supabase_client

# Create FastAPI app
app = FastAPI(
    title="Doc Maker API",
    description="API for document generation with digital signatures",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
os.makedirs("templates", exist_ok=True)
os.makedirs("generated", exist_ok=True)
os.makedirs("signatures", exist_ok=True)

# Security
security = HTTPBearer()

# Include routers
app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(templates.router, prefix="/api/templates", tags=["templates"])
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])

@app.get("/")
async def root():
    return {"message": "Doc Maker API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
