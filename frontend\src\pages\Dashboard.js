import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FileText, Upload, History, Plus, TrendingUp, Clock, CheckCircle } from 'lucide-react'
import { templatesApi, documentsApi } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'
import toast from 'react-hot-toast'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalTemplates: 0,
    totalDocuments: 0,
    recentDocuments: [],
    recentTemplates: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      const [templates, documents] = await Promise.all([
        templatesApi.getAll(),
        documentsApi.getAll()
      ])

      setStats({
        totalTemplates: templates.length,
        totalDocuments: documents.length,
        recentDocuments: documents.slice(0, 5),
        recentTemplates: templates.slice(0, 5)
      })
    } catch (error) {
      toast.error('Failed to load dashboard data')
      console.error('Dashboard error:', error)
    } finally {
      setLoading(false)
    }
  }

  const quickActions = [
    {
      title: 'Upload Template',
      description: 'Upload a new .docx template with placeholders',
      icon: Upload,
      link: '/templates',
      color: 'bg-blue-500'
    },
    {
      title: 'Create Document',
      description: 'Generate a new document from existing templates',
      icon: Plus,
      link: '/create-document',
      color: 'bg-green-500'
    },
    {
      title: 'View History',
      description: 'Browse and download your generated documents',
      icon: History,
      link: '/documents',
      color: 'bg-purple-500'
    }
  ]

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">
          Welcome to Doc Maker. Create professional documents with digital signatures.
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100">
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{stats.totalTemplates}</h3>
              <p className="text-gray-600">Templates</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">{stats.totalDocuments}</h3>
              <p className="text-gray-600">Documents Generated</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100">
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <h3 className="text-2xl font-bold text-gray-900">
                {stats.totalDocuments > 0 ? '100%' : '0%'}
              </h3>
              <p className="text-gray-600">Success Rate</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => {
            const Icon = action.icon
            return (
              <Link
                key={index}
                to={action.link}
                className="card hover:shadow-md transition-shadow duration-200 group"
              >
                <div className="flex items-center mb-3">
                  <div className={`p-2 rounded-lg ${action.color}`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                  {action.title}
                </h3>
                <p className="text-gray-600 text-sm">{action.description}</p>
              </Link>
            )
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Documents */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Recent Documents</h2>
            <Link
              to="/documents"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              View All
            </Link>
          </div>
          
          {stats.recentDocuments.length > 0 ? (
            <div className="space-y-3">
              {stats.recentDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <FileText className="w-4 h-4 text-green-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {doc.docx_filename}
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="w-3 h-3 mr-1" />
                      {new Date(doc.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No documents generated yet</p>
              <Link to="/create-document" className="text-primary-600 hover:text-primary-700 text-sm">
                Create your first document
              </Link>
            </div>
          )}
        </div>

        {/* Recent Templates */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Recent Templates</h2>
            <Link
              to="/templates"
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              View All
            </Link>
          </div>
          
          {stats.recentTemplates.length > 0 ? (
            <div className="space-y-3">
              {stats.recentTemplates.map((template) => (
                <div key={template.id} className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Upload className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {template.original_filename}
                    </p>
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock className="w-3 h-3 mr-1" />
                      {new Date(template.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Upload className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No templates uploaded yet</p>
              <Link to="/templates" className="text-primary-600 hover:text-primary-700 text-sm">
                Upload your first template
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
