import { uploadFile, downloadFile, deleteFile, getPublicUrl, createSignedUrl, getCurrentUser } from './supabase'

class StorageService {
  constructor() {
    this.maxFileSize = parseInt(process.env.REACT_APP_MAX_FILE_SIZE) || 10485760 // 10MB
    this.allowedTypes = process.env.REACT_APP_ALLOWED_FILE_TYPES?.split(',') || ['.docx', '.doc']
  }

  validateFile(file) {
    const errors = []

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size exceeds maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`)
    }

    // Check file type
    const fileName = file.name.toLowerCase()
    const isValidType = this.allowedTypes.some(type => fileName.endsWith(type.toLowerCase()))

    if (!isValidType) {
      errors.push(`File type not allowed. Allowed types: ${this.allowedTypes.join(', ')}`)
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async uploadTemplate(file) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      const validation = this.validateFile(file)
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '))
      }

      // Generate unique filename
      const timestamp = Date.now()
      const randomId = Math.random().toString(36).substring(2, 15)
      const fileExtension = file.name.split('.').pop()
      const uniqueFileName = `${timestamp}_${randomId}.${fileExtension}`
      const filePath = `templates/${user.id}/${uniqueFileName}`

      const { data, error } = await uploadFile(file, filePath)

      if (error) {
        throw new Error(error.message || 'Upload failed')
      }

      return {
        success: true,
        filePath,
        originalName: file.name,
        fileName: uniqueFileName,
        size: file.size,
        url: getPublicUrl(filePath)
      }
    } catch (error) {
      console.error('Template upload error:', error)
      return {
        success: false,
        error: error.message || 'Upload failed'
      }
    }
  }

  async uploadSignature(file) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Validate image file
      if (!file.type.startsWith('image/')) {
        throw new Error('Only image files are allowed for signatures')
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit for images
        throw new Error('Image size exceeds 5MB limit')
      }

      // Generate unique filename
      const timestamp = Date.now()
      const randomId = Math.random().toString(36).substring(2, 15)
      const fileExtension = file.name.split('.').pop()
      const uniqueFileName = `${timestamp}_${randomId}.${fileExtension}`
      const filePath = `signatures/${user.id}/${uniqueFileName}`

      const { data, error } = await uploadFile(file, filePath)

      if (error) {
        throw new Error(error.message || 'Upload failed')
      }

      return {
        success: true,
        filePath,
        originalName: file.name,
        fileName: uniqueFileName,
        size: file.size,
        url: getPublicUrl(filePath)
      }
    } catch (error) {
      console.error('Signature upload error:', error)
      return {
        success: false,
        error: error.message || 'Upload failed'
      }
    }
  }

  async downloadFile(filePath) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Security check - ensure user can only access their own files
      if (!filePath.includes(`/${user.id}/`)) {
        throw new Error('Access denied')
      }

      const { data, error } = await downloadFile(filePath)

      if (error) {
        throw new Error(error.message || 'Download failed')
      }

      return {
        success: true,
        data
      }
    } catch (error) {
      console.error('File download error:', error)
      return {
        success: false,
        error: error.message || 'Download failed'
      }
    }
  }

  async deleteFile(filePath) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Security check - ensure user can only delete their own files
      if (!filePath.includes(`/${user.id}/`)) {
        throw new Error('Access denied')
      }

      const { data, error } = await deleteFile(filePath)

      if (error) {
        throw new Error(error.message || 'Delete failed')
      }

      return {
        success: true
      }
    } catch (error) {
      console.error('File delete error:', error)
      return {
        success: false,
        error: error.message || 'Delete failed'
      }
    }
  }

  async getSignedUrl(filePath, expiresIn = 3600) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Security check
      if (!filePath.includes(`/${user.id}/`)) {
        throw new Error('Access denied')
      }

      const { data, error } = await createSignedUrl(filePath, expiresIn)

      if (error) {
        throw new Error(error.message || 'Failed to create signed URL')
      }

      return {
        success: true,
        signedUrl: data.signedUrl
      }
    } catch (error) {
      console.error('Signed URL error:', error)
      return {
        success: false,
        error: error.message || 'Failed to create signed URL'
      }
    }
  }

  getPublicUrl(filePath) {
    return getPublicUrl(filePath)
  }

  // Helper method to create a download link
  createDownloadLink(blob, filename) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
}

export const storageService = new StorageService()
export default storageService
