import os
import uuid
import re
from pathlib import Path
from typing import List, Dict, Any
from fastapi import H<PERSON>P<PERSON>x<PERSON>, UploadFile
from docxtpl import DocxTemplate
from docx import Document
import json
from ..utils.supabase_client import get_supabase_client

class TemplateService:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.templates_dir = Path("templates")
        self.templates_dir.mkdir(exist_ok=True)
    
    async def upload_template(self, file: UploadFile, user_id: str) -> Dict[str, Any]:
        """Upload and process a .docx template"""
        if not file.filename.endswith('.docx'):
            raise HTTPException(status_code=400, detail="Only .docx files are allowed")
        
        # Generate unique filename
        template_id = str(uuid.uuid4())
        filename = f"{template_id}_{file.filename}"
        file_path = self.templates_dir / filename
        
        # Save file
        try:
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to save template: {str(e)}")
        
        # Extract placeholders
        placeholders = self._extract_placeholders(file_path)
        
        # Save template metadata to Supabase
        template_data = {
            "id": template_id,
            "user_id": user_id,
            "original_filename": file.filename,
            "filename": filename,
            "placeholders": placeholders,
            "created_at": "now()"
        }
        
        try:
            result = self.supabase.table("templates").insert(template_data).execute()
            return {
                "id": template_id,
                "filename": file.filename,
                "placeholders": placeholders,
                "created_at": result.data[0]["created_at"] if result.data else None
            }
        except Exception as e:
            # Clean up file if database insert fails
            if file_path.exists():
                file_path.unlink()
            raise HTTPException(status_code=500, detail=f"Failed to save template metadata: {str(e)}")
    
    def _extract_placeholders(self, file_path: Path) -> List[Dict[str, str]]:
        """Extract placeholders from .docx template"""
        try:
            doc = DocxTemplate(str(file_path))
            
            # Read the document XML to find placeholders
            placeholders_set = set()
            
            # Extract from document text
            for paragraph in doc.docx.paragraphs:
                text = paragraph.text
                matches = re.findall(r'\{\{(\w+)\}\}', text)
                placeholders_set.update(matches)
            
            # Extract from tables
            for table in doc.docx.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text = cell.text
                        matches = re.findall(r'\{\{(\w+)\}\}', text)
                        placeholders_set.update(matches)
            
            # Convert to list of dictionaries with field info
            placeholders = []
            for placeholder in sorted(placeholders_set):
                field_type = self._guess_field_type(placeholder)
                placeholders.append({
                    "name": placeholder,
                    "type": field_type,
                    "label": self._format_label(placeholder),
                    "required": True
                })
            
            return placeholders
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to extract placeholders: {str(e)}")
    
    def _guess_field_type(self, placeholder: str) -> str:
        """Guess the appropriate input type for a placeholder"""
        placeholder_lower = placeholder.lower()
        
        if any(keyword in placeholder_lower for keyword in ['date', 'time']):
            return 'date'
        elif any(keyword in placeholder_lower for keyword in ['email', 'mail']):
            return 'email'
        elif any(keyword in placeholder_lower for keyword in ['phone', 'mobile', 'tel']):
            return 'tel'
        elif any(keyword in placeholder_lower for keyword in ['number', 'no', 'qty', 'amount', 'price']):
            return 'number'
        elif any(keyword in placeholder_lower for keyword in ['description', 'comment', 'note', 'remarks']):
            return 'textarea'
        else:
            return 'text'
    
    def _format_label(self, placeholder: str) -> str:
        """Format placeholder name into a readable label"""
        # Convert camelCase and snake_case to readable format
        formatted = re.sub(r'([A-Z])', r' \1', placeholder)
        formatted = formatted.replace('_', ' ').replace('-', ' ')
        return formatted.title().strip()
    
    async def get_user_templates(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all templates for a user"""
        try:
            result = self.supabase.table("templates").select("*").eq("user_id", user_id).execute()
            return result.data if result.data else []
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to fetch templates: {str(e)}")
    
    async def get_template_placeholders(self, template_id: str, user_id: str) -> Dict[str, Any]:
        """Get template placeholders and metadata"""
        try:
            result = self.supabase.table("templates").select("*").eq("id", template_id).eq("user_id", user_id).execute()
            
            if not result.data:
                raise HTTPException(status_code=404, detail="Template not found")
            
            template = result.data[0]
            return {
                "id": template["id"],
                "filename": template["original_filename"],
                "placeholders": template["placeholders"]
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to fetch template: {str(e)}")
    
    def get_template_path(self, template_id: str, user_id: str) -> Path:
        """Get the file path for a template"""
        try:
            result = self.supabase.table("templates").select("filename").eq("id", template_id).eq("user_id", user_id).execute()
            
            if not result.data:
                raise HTTPException(status_code=404, detail="Template not found")
            
            filename = result.data[0]["filename"]
            file_path = self.templates_dir / filename
            
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="Template file not found")
            
            return file_path
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get template path: {str(e)}")

template_service = TemplateService()
