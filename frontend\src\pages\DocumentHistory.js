import React, { useState, useEffect } from 'react'
import { Download, Calendar, FileText, Signature, Search, Filter } from 'lucide-react'
import { documentsApi } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'
import toast from 'react-hot-toast'

const DocumentHistory = () => {
  const [documents, setDocuments] = useState([])
  const [filteredDocuments, setFilteredDocuments] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')

  useEffect(() => {
    loadDocuments()
  }, [])

  useEffect(() => {
    filterDocuments()
  }, [documents, searchTerm, filterType])

  const loadDocuments = async () => {
    try {
      const data = await documentsApi.getAll()
      setDocuments(data)
    } catch (error) {
      toast.error('Failed to load documents')
      console.error('Documents error:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterDocuments = () => {
    let filtered = documents

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(doc =>
        (doc.docx_filename && doc.docx_filename.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (doc.pdf_filename && doc.pdf_filename.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Apply type filter
    if (filterType === 'with_signature') {
      filtered = filtered.filter(doc => doc.has_signature)
    } else if (filterType === 'without_signature') {
      filtered = filtered.filter(doc => !doc.has_signature)
    }

    setFilteredDocuments(filtered)
  }

  const handleDownload = async (documentId, fileType, filename) => {
    try {
      const response = await documentsApi.download(documentId, fileType)
      
      // Create blob and download
      const blob = new Blob([response.data], {
        type: fileType === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename || `document.${fileType}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success(`${fileType.toUpperCase()} downloaded successfully!`)
    } catch (error) {
      toast.error(`Failed to download ${fileType.toUpperCase()}`)
      console.error('Download error:', error)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Document History</h1>
        <p className="text-gray-600">
          View and download your generated documents
        </p>
      </div>

      {/* Search and Filter */}
      <div className="card mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="form-input pl-10 w-full sm:w-48"
            >
              <option value="all">All Documents</option>
              <option value="with_signature">With Signature</option>
              <option value="without_signature">Without Signature</option>
            </select>
          </div>
        </div>
      </div>

      {/* Documents List */}
      {filteredDocuments.length > 0 ? (
        <div className="space-y-4">
          {filteredDocuments.map((document) => (
            <div key={document.id} className="card hover:shadow-md transition-shadow">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div className="flex-1">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                      <FileText className="w-5 h-5 text-blue-600" />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {document.docx_filename}
                      </h3>
                      
                      <div className="flex flex-wrap items-center gap-4 mt-2 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>Created {formatDate(document.created_at)}</span>
                        </div>
                        
                        {document.has_signature && (
                          <div className="flex items-center space-x-1 text-green-600">
                            <Signature className="w-4 h-4" />
                            <span>With Signature</span>
                          </div>
                        )}
                      </div>

                      {/* Document metadata */}
                      {document.form_data && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Form Data Preview:</h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                            {Object.entries(JSON.parse(document.form_data)).slice(0, 6).map(([key, value]) => (
                              <div key={key} className="truncate">
                                <span className="font-medium text-gray-600">{key}:</span>{' '}
                                <span className="text-gray-800">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-2 mt-4 lg:mt-0 lg:ml-6">
                  <button
                    onClick={() => handleDownload(document.id, 'docx', document.docx_filename)}
                    className="btn-outline flex items-center justify-center space-x-2 text-sm py-2 px-3"
                  >
                    <Download className="w-4 h-4" />
                    <span>DOCX</span>
                  </button>
                  
                  {document.pdf_filename && (
                    <button
                      onClick={() => handleDownload(document.id, 'pdf', document.pdf_filename)}
                      className="btn-primary flex items-center justify-center space-x-2 text-sm py-2 px-3"
                    >
                      <Download className="w-4 h-4" />
                      <span>PDF</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          {documents.length === 0 ? (
            <>
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No documents generated yet
              </h3>
              <p className="text-gray-600 mb-4">
                Create your first document to see it here
              </p>
              <button
                onClick={() => window.location.href = '/create-document'}
                className="btn-primary"
              >
                Create Document
              </button>
            </>
          ) : (
            <>
              <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No documents match your search
              </h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search terms or filters
              </p>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setFilterType('all')
                }}
                className="btn-outline"
              >
                Clear Filters
              </button>
            </>
          )}
        </div>
      )}

      {/* Stats Footer */}
      {documents.length > 0 && (
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <div className="flex flex-wrap items-center justify-center gap-8 text-sm text-gray-600">
            <div>
              <span className="font-medium text-gray-900">{documents.length}</span> total documents
            </div>
            <div>
              <span className="font-medium text-gray-900">
                {documents.filter(doc => doc.has_signature).length}
              </span> with signatures
            </div>
            <div>
              <span className="font-medium text-gray-900">
                {documents.filter(doc => doc.pdf_filename).length}
              </span> PDF versions available
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default DocumentHistory
