import React, { useRef, useEffect, useState } from 'react'
import { RotateCcw, Check, X } from 'lucide-react'
import SignaturePad from 'signature_pad'

const SignatureCapture = ({ onSave, onCancel, isOpen }) => {
  const canvasRef = useRef(null)
  const [signaturePad, setSignaturePad] = useState(null)
  const [isEmpty, setIsEmpty] = useState(true)

  useEffect(() => {
    if (isOpen && canvasRef.current) {
      const canvas = canvasRef.current
      const ratio = Math.max(window.devicePixelRatio || 1, 1)
      
      // Set canvas size
      canvas.width = canvas.offsetWidth * ratio
      canvas.height = canvas.offsetHeight * ratio
      canvas.getContext('2d').scale(ratio, ratio)
      
      // Initialize signature pad
      const pad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)',
        velocityFilterWeight: 0.7,
        minWidth: 0.5,
        maxWidth: 2.5,
        throttle: 16,
        minDistance: 5,
      })

      // Listen for signature events
      pad.addEventListener('beginStroke', () => setIsEmpty(false))
      
      setSignaturePad(pad)
      setIsEmpty(true)

      return () => {
        pad.off()
      }
    }
  }, [isOpen])

  const handleClear = () => {
    if (signaturePad) {
      signaturePad.clear()
      setIsEmpty(true)
    }
  }

  const handleSave = () => {
    if (signaturePad && !signaturePad.isEmpty()) {
      const dataURL = signaturePad.toDataURL('image/png')
      onSave(dataURL)
    }
  }

  const handleCancel = () => {
    handleClear()
    onCancel()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Digital Signature
          </h3>
          
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4">
            <p className="text-sm text-gray-600 text-center mb-4">
              Sign in the box below using your mouse or touch screen
            </p>
            
            <div className="bg-white border border-gray-200 rounded">
              <canvas
                ref={canvasRef}
                className="w-full h-48 rounded cursor-crosshair"
                style={{ touchAction: 'none' }}
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={handleClear}
              className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Clear</span>
            </button>
            
            <button
              onClick={handleCancel}
              className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200"
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
            
            <button
              onClick={handleSave}
              disabled={isEmpty}
              className="flex items-center justify-center space-x-2 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex-1"
            >
              <Check className="w-4 h-4" />
              <span>Save Signature</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignatureCapture
