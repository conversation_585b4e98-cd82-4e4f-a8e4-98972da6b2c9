# =============================================================================
# SUPABASE CONFIGURATION (Client-side)
# =============================================================================
# Your Supabase project URL (found in Project Settings > API)
REACT_APP_SUPABASE_URL=https://your-project-id.supabase.co

# Anonymous/Public Key (safe to use in client-side code)
# This key respects Row Level Security policies
# Found in Project Settings > API > anon public key
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API URL
# Development: http://localhost:8000
# Production: https://your-backend-domain.com
REACT_APP_API_URL=http://localhost:8000

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment: development, staging, production
REACT_APP_ENVIRONMENT=development

# Application name
REACT_APP_APP_NAME=Doc Maker

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size in bytes (10MB = 10485760)
REACT_APP_MAX_FILE_SIZE=10485760

# Allowed file types (comma-separated)
REACT_APP_ALLOWED_FILE_TYPES=.docx,.doc

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# Supabase Storage bucket name
REACT_APP_STORAGE_BUCKET=documents

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# For Vercel deployment:
# 1. Go to your Vercel project dashboard
# 2. Navigate to Settings > Environment Variables
# 3. Add all REACT_APP_* variables above
# 4. Set REACT_APP_API_URL to your production backend URL
#
# For Netlify deployment:
# 1. Go to Site settings > Environment variables
# 2. Add all REACT_APP_* variables above
#
# Note: Only variables prefixed with REACT_APP_ are available in the browser
