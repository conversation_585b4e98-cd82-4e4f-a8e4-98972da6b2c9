from fastapi import HTT<PERSON>Ex<PERSON>, status
from typing import Optional, Dict, Any
import os
from dotenv import load_dotenv

load_dotenv()

# Mock users for local testing
mock_users = {
    "<EMAIL>": {
        "id": "test-user",
        "email": "<EMAIL>",
        "password": "password123"  # In real app, this would be hashed
    }
}

class AuthService:
    def __init__(self):
        pass  # No Supabase client needed for mock
    
    async def authenticate_user(self, token: str) -> dict:
        """Mock authenticate user - always return test user for local testing"""
        # For local testing, just return a test user
        return {
            "id": "test-user",
            "email": "<EMAIL>",
            "user_metadata": {}
        }
    
    async def register_user(self, email: str, password: str) -> dict:
        """Mock register user - always succeeds for local testing"""
        # For local testing, add user to mock storage and return success
        user_id = f"user-{len(mock_users) + 1}"
        mock_users[email] = {
            "id": user_id,
            "email": email,
            "password": password
        }
        
        return {
            "user": {
                "id": user_id,
                "email": email
            },
            "session": {
                "access_token": f"mock-token-{user_id}",
                "refresh_token": f"mock-refresh-{user_id}"
            }
        }
    
    async def login_user(self, email: str, password: str) -> dict:
        """Login user"""
        try:
            response = self.supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if response.user and response.session:
                return {
                    "user": {
                        "id": response.user.id,
                        "email": response.user.email
                    },
                    "session": {
                        "access_token": response.session.access_token,
                        "refresh_token": response.session.refresh_token
                    }
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid credentials"
                )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

auth_service = AuthService()
