import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid
from dotenv import load_dotenv

load_dotenv()

# Mock data storage for local testing
mock_templates = {}
mock_documents = {}

# Create storage directories
os.makedirs("storage/templates", exist_ok=True)
os.makedirs("storage/generated", exist_ok=True)
os.makedirs("storage/signatures", exist_ok=True)

class MockSupabaseTable:
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.data_store = mock_templates if table_name == "templates" else mock_documents
        self._select_fields = "*"
        self._filters = []
        self._order = None
    
    def select(self, fields: str = "*"):
        self._select_fields = fields
        return self
    
    def eq(self, column: str, value: Any):
        self._filters.append(("eq", column, value))
        return self
    
    def order(self, column: str, desc: bool = False):
        self._order = (column, desc)
        return self
    
    def insert(self, data: Dict[str, Any]):
        # Generate ID if not provided
        if "id" not in data:
            import uuid
            data["id"] = str(uuid.uuid4())
        
        # Add timestamp if not provided
        if "created_at" not in data:
            data["created_at"] = datetime.now().isoformat()
        
        # Store data
        self.data_store[data["id"]] = data
        
        # Mock response
        return MockResponse([data])
    
    def execute(self):
        # Apply filters
        results = list(self.data_store.values())
        
        for filter_type, column, value in self._filters:
            if filter_type == "eq":
                results = [item for item in results if item.get(column) == value]
        
        # Apply ordering
        if self._order:
            column, desc = self._order
            results.sort(key=lambda x: x.get(column, ""), reverse=desc)
        
        return MockResponse(results)

class MockResponse:
    def __init__(self, data: List[Dict[str, Any]]):
        self.data = data

class MockSupabaseClient:
    def table(self, name: str):
        return MockSupabaseTable(name)


def get_supabase_client():
    """Get mock Supabase client for local testing"""
    return MockSupabaseClient()
