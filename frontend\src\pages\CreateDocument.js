import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { FileText, Download, Signature, AlertCircle, CheckCircle } from 'lucide-react'
import { templatesApi, documentsApi } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'
import SignatureCapture from '../components/SignatureCapture'
import toast from 'react-hot-toast'

const CreateDocument = () => {
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [templateData, setTemplateData] = useState(null)
  const [formData, setFormData] = useState({})
  const [signature, setSignature] = useState(null)
  const [showSignature, setShowSignature] = useState(false)
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [generatedDocument, setGeneratedDocument] = useState(null)
  const navigate = useNavigate()

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      const data = await templatesApi.getAll()
      setTemplates(data)
    } catch (error) {
      toast.error('Failed to load templates')
      console.error('Templates error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTemplateSelect = async (templateId) => {
    if (!templateId) {
      setSelectedTemplate(null)
      setTemplateData(null)
      setFormData({})
      return
    }

    try {
      const data = await templatesApi.getPlaceholders(templateId)
      setSelectedTemplate(templateId)
      setTemplateData(data)
      
      // Initialize form data with empty values
      const initialData = {}
      data.placeholders.forEach(placeholder => {
        initialData[placeholder.name] = ''
      })
      setFormData(initialData)
    } catch (error) {
      toast.error('Failed to load template data')
      console.error('Template data error:', error)
    }
  }

  const handleInputChange = (fieldName, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  const handleSignatureSave = (signatureData) => {
    setSignature(signatureData)
    setShowSignature(false)
    toast.success('Signature captured!')
  }

  const handleSignatureCancel = () => {
    setShowSignature(false)
  }

  const renderFormField = (placeholder) => {
    const { name, type, label, required } = placeholder
    const value = formData[name] || ''

    const commonProps = {
      id: name,
      value,
      onChange: (e) => handleInputChange(name, e.target.value),
      required,
      className: 'form-input'
    }

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows="3"
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        )
      case 'date':
        return (
          <input
            {...commonProps}
            type="date"
          />
        )
      case 'number':
        return (
          <input
            {...commonProps}
            type="number"
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        )
      case 'email':
        return (
          <input
            {...commonProps}
            type="email"
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        )
      case 'tel':
        return (
          <input
            {...commonProps}
            type="tel"
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        )
      default:
        return (
          <input
            {...commonProps}
            type="text"
            placeholder={`Enter ${label.toLowerCase()}`}
          />
        )
    }
  }

  const validateForm = () => {
    if (!templateData) return false
    
    for (const placeholder of templateData.placeholders) {
      if (placeholder.required && !formData[placeholder.name]?.trim()) {
        toast.error(`${placeholder.label} is required`)
        return false
      }
    }
    return true
  }

  const handleGenerateDocument = async () => {
    if (!validateForm()) return

    setGenerating(true)
    try {
      const result = await documentsApi.generate(
        selectedTemplate,
        formData,
        signature
      )
      
      setGeneratedDocument(result)
      toast.success('Document generated successfully!')
    } catch (error) {
      toast.error('Failed to generate document')
      console.error('Generation error:', error)
    } finally {
      setGenerating(false)
    }
  }

  const handleDownload = async (fileType) => {
    if (!generatedDocument) return

    try {
      const response = await documentsApi.download(generatedDocument.id, fileType)
      
      // Create blob and download
      const blob = new Blob([response.data], {
        type: fileType === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = generatedDocument[`${fileType}_filename`] || `document.${fileType}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast.success(`${fileType.toUpperCase()} downloaded successfully!`)
    } catch (error) {
      toast.error(`Failed to download ${fileType.toUpperCase()}`)
      console.error('Download error:', error)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Document</h1>
        <p className="text-gray-600">
          Generate professional documents from your templates with digital signatures
        </p>
      </div>

      {templates.length === 0 ? (
        <div className="card text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates available
          </h3>
          <p className="text-gray-600 mb-4">
            You need to upload at least one template before creating documents
          </p>
          <button
            onClick={() => navigate('/templates')}
            className="btn-primary"
          >
            Upload Template
          </button>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Template Selection */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              1. Select Template
            </h2>
            <select
              value={selectedTemplate || ''}
              onChange={(e) => handleTemplateSelect(e.target.value)}
              className="form-input"
            >
              <option value="">Choose a template...</option>
              {templates.map((template) => (
                <option key={template.id} value={template.id}>
                  {template.original_filename || template.filename}
                </option>
              ))}
            </select>
          </div>

          {/* Dynamic Form */}
          {templateData && (
            <div className="card">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                2. Fill Form Data
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {templateData.placeholders.map((placeholder) => (
                  <div key={placeholder.name}>
                    <label htmlFor={placeholder.name} className="form-label">
                      {placeholder.label}
                      {placeholder.required && (
                        <span className="text-red-500 ml-1">*</span>
                      )}
                    </label>
                    {renderFormField(placeholder)}
                  </div>
                ))}
              </div>
              
              {/* Signature Section */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Digital Signature
                </h3>
                <div className="flex items-center space-x-4">
                  {signature ? (
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2 text-green-600">
                        <CheckCircle className="w-5 h-5" />
                        <span className="text-sm font-medium">Signature captured</span>
                      </div>
                      <button
                        onClick={() => setShowSignature(true)}
                        className="btn-outline text-sm"
                      >
                        Update Signature
                      </button>
                      <button
                        onClick={() => setSignature(null)}
                        className="btn-secondary text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2 text-amber-600">
                        <AlertCircle className="w-5 h-5" />
                        <span className="text-sm">No signature captured</span>
                      </div>
                      <button
                        onClick={() => setShowSignature(true)}
                        className="btn-outline flex items-center space-x-2"
                      >
                        <Signature className="w-4 h-4" />
                        <span>Add Signature</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Generate Button */}
          {templateData && (
            <div className="card">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                3. Generate Document
              </h2>
              <button
                onClick={handleGenerateDocument}
                disabled={generating}
                className="btn-primary flex items-center space-x-2"
              >
                {generating ? (
                  <LoadingSpinner size="small" />
                ) : (
                  <FileText className="w-4 h-4" />
                )}
                <span>{generating ? 'Generating...' : 'Generate Document'}</span>
              </button>
            </div>
          )}

          {/* Download Section */}
          {generatedDocument && (
            <div className="card bg-green-50 border-green-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">
                    Document Generated Successfully!
                  </h2>
                  <p className="text-gray-600">
                    Your document is ready for download
                  </p>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={() => handleDownload('docx')}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Download DOCX</span>
                </button>
                
                {generatedDocument.pdf_available && (
                  <button
                    onClick={() => handleDownload('pdf')}
                    className="btn-outline flex items-center space-x-2"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download PDF</span>
                  </button>
                )}
                
                <button
                  onClick={() => navigate('/documents')}
                  className="btn-secondary"
                >
                  View All Documents
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Signature Capture Modal */}
      <SignatureCapture
        isOpen={showSignature}
        onSave={handleSignatureSave}
        onCancel={handleSignatureCancel}
      />
    </div>
  )
}

export default CreateDocument
