# Doc Maker API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
All API endpoints (except health checks) require authentication via Bearer token in the Authorization header:
```
Authorization: Bearer <supabase_access_token>
```

## API Endpoints

### Health Check
- **GET** `/` - Root endpoint
- **GET** `/health` - Health check endpoint

### Authentication Endpoints

#### Register User
- **POST** `/api/auth/register`
- **Body:**
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response:**
  ```json
  {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>"
    },
    "session": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token"
    }
  }
  ```

#### Login User
- **POST** `/api/auth/login`
- **Body:** Same as register
- **Response:** Same as register

#### Get Current User
- **GET** `/api/auth/me`
- **Headers:** `Authorization: Bearer <token>`
- **Response:**
  ```json
  {
    "id": "uuid",
    "email": "<EMAIL>"
  }
  ```

### Template Endpoints

#### Upload Template
- **POST** `/api/templates/upload`
- **Headers:** 
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **Body:** Form data with file field containing .docx file
- **Response:**
  ```json
  {
    "id": "uuid",
    "filename": "template.docx",
    "placeholders": [
      {
        "name": "TrainNo",
        "type": "text",
        "label": "Train No",
        "required": true
      }
    ],
    "created_at": "2024-01-01T00:00:00Z"
  }
  ```

#### Get All Templates
- **GET** `/api/templates/`
- **Headers:** `Authorization: Bearer <token>`
- **Response:** Array of template objects

#### Get Template Placeholders
- **GET** `/api/templates/{template_id}/placeholders`
- **Headers:** `Authorization: Bearer <token>`
- **Response:**
  ```json
  {
    "id": "uuid",
    "filename": "template.docx",
    "placeholders": [...]
  }
  ```

### Document Endpoints

#### Generate Document
- **POST** `/api/documents/generate`
- **Headers:** `Authorization: Bearer <token>`
- **Body:**
  ```json
  {
    "template_id": "uuid",
    "form_data": {
      "TrainNo": "12345",
      "LocoNo": "ABC123",
      "Date": "2024-01-01"
    },
    "signature_data": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." // Optional
  }
  ```
- **Response:**
  ```json
  {
    "id": "uuid",
    "docx_available": true,
    "pdf_available": true,
    "created_at": "2024-01-01T00:00:00Z",
    "docx_filename": "document.docx",
    "pdf_filename": "document.pdf"
  }
  ```

#### Download Document
- **GET** `/api/documents/{document_id}/download/{file_type}`
- **Headers:** `Authorization: Bearer <token>`
- **Parameters:**
  - `document_id`: UUID of the document
  - `file_type`: Either "docx" or "pdf"
- **Response:** File download with appropriate content-type headers

#### Get All Documents
- **GET** `/api/documents/`
- **Headers:** `Authorization: Bearer <token>`
- **Response:** Array of document objects with metadata

## Error Responses

All endpoints return errors in the following format:
```json
{
  "detail": "Error message description"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid or missing token)
- `404` - Not Found (resource doesn't exist)
- `422` - Unprocessable Entity (validation errors)
- `500` - Internal Server Error

## File Upload Requirements

### Template Files (.docx)
- Must be valid Microsoft Word documents
- Should contain placeholders in the format `{{FieldName}}`
- Maximum file size: 10MB (configurable)

### Supported Placeholder Types
The system automatically detects field types based on naming conventions:
- `text` - Default type for most fields
- `number` - Fields containing "no", "qty", "amount", "price"
- `date` - Fields containing "date", "time"
- `email` - Fields containing "email", "mail"
- `tel` - Fields containing "phone", "mobile", "tel"
- `textarea` - Fields containing "description", "comment", "note", "remarks"

### Signature Data Format
- Base64 encoded PNG image
- Supports data URL format: `data:image/png;base64,{base64_data}`
- Automatically processed to remove white background
- Embedded above supervisor signature area in documents

## Rate Limits
- 100 requests per minute per user for most endpoints
- 10 file uploads per minute per user
- Large file processing may take 30-60 seconds

## Development Notes
- PDF conversion requires Windows with Microsoft Word installed
- Template and document files are stored locally in `templates/` and `generated/` directories
- Signature images are stored in `signatures/` directory
- All file paths are relative to the backend application root
