import React, { useState, useEffect } from 'react'
import { Upload, FileText, Trash2, Eye, Calendar, Tag } from 'lucide-react'
import { templatesApi } from '../services/api'
import LoadingSpinner from '../components/LoadingSpinner'
import toast from 'react-hot-toast'

const Templates = () => {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [showPlaceholders, setShowPlaceholders] = useState(false)

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      const data = await templatesApi.getAll()
      setTemplates(data)
    } catch (error) {
      toast.error('Failed to load templates')
      console.error('Templates error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    if (!file.name.endsWith('.docx')) {
      toast.error('Please select a .docx file')
      return
    }

    setUploading(true)
    try {
      const result = await templatesApi.upload(file)
      toast.success('Template uploaded successfully!')
      setTemplates([result, ...templates])
    } catch (error) {
      toast.error('Failed to upload template')
      console.error('Upload error:', error)
    } finally {
      setUploading(false)
      event.target.value = '' // Reset file input
    }
  }

  const handleViewPlaceholders = async (template) => {
    try {
      const data = await templatesApi.getPlaceholders(template.id)
      setSelectedTemplate(data)
      setShowPlaceholders(true)
    } catch (error) {
      toast.error('Failed to load template placeholders')
      console.error('Placeholders error:', error)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Templates</h1>
          <p className="text-gray-600">
            Upload and manage your .docx templates with placeholders
          </p>
        </div>
        
        <div className="mt-4 sm:mt-0">
          <label className="btn-primary cursor-pointer inline-flex items-center space-x-2">
            {uploading ? (
              <LoadingSpinner size="small" />
            ) : (
              <Upload className="w-4 h-4" />
            )}
            <span>{uploading ? 'Uploading...' : 'Upload Template'}</span>
            <input
              type="file"
              accept=".docx"
              onChange={handleFileUpload}
              disabled={uploading}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Upload Area */}
      <div className="card mb-8 border-2 border-dashed border-gray-300 hover:border-primary-400 transition-colors">
        <div className="text-center py-8">
          <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Upload .docx Template
          </h3>
          <p className="text-gray-600 mb-4">
            Select a Word document (.docx) with placeholders like {{`TrainNo`}}, {{`LocoNo`}}, etc.
          </p>
          <label className="btn-outline cursor-pointer inline-flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Choose File</span>
            <input
              type="file"
              accept=".docx"
              onChange={handleFileUpload}
              disabled={uploading}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Templates List */}
      {templates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div key={template.id} className="card hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2 truncate">
                {template.original_filename || template.filename}
              </h3>
              
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Tag className="w-4 h-4 mr-2" />
                  <span>{template.placeholders?.length || 0} placeholders</span>
                </div>
                
                <div className="flex items-center text-sm text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>{formatDate(template.created_at)}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => handleViewPlaceholders(template)}
                  className="flex-1 btn-outline text-sm py-2 px-3 flex items-center justify-center space-x-1"
                >
                  <Eye className="w-4 h-4" />
                  <span>View Fields</span>
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No templates yet
          </h3>
          <p className="text-gray-600 mb-4">
            Upload your first .docx template to get started
          </p>
        </div>
      )}

      {/* Placeholders Modal */}
      {showPlaceholders && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Template Fields: {selectedTemplate.filename}
                </h3>
                <button
                  onClick={() => setShowPlaceholders(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-96">
              {selectedTemplate.placeholders?.length > 0 ? (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600 mb-4">
                    The following fields will be available when creating documents from this template:
                  </p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {selectedTemplate.placeholders.map((placeholder, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <div className="font-medium text-gray-900 mb-1">
                          {placeholder.label}
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          Field: {{`${placeholder.name}`}}
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          placeholder.type === 'text' ? 'bg-blue-100 text-blue-800' :
                          placeholder.type === 'number' ? 'bg-green-100 text-green-800' :
                          placeholder.type === 'date' ? 'bg-purple-100 text-purple-800' :
                          placeholder.type === 'email' ? 'bg-red-100 text-red-800' :
                          placeholder.type === 'textarea' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {placeholder.type}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-gray-600">No placeholders found in this template.</p>
              )}
            </div>
            
            <div className="p-6 border-t border-gray-200">
              <button
                onClick={() => setShowPlaceholders(false)}
                className="btn-primary w-full"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Templates
