import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY
const storageBucket = process.env.REACT_APP_STORAGE_BUCKET || 'documents'

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Auth helpers
export const signUp = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    })

    if (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('Sign up exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}

export const signIn = async (email, password) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Sign in error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('Sign in exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}

export const signOut = async () => {
  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      console.error('Sign out error:', error)
    }

    return { error }
  } catch (err) {
    console.error('Sign out exception:', err)
    return { error: { message: 'An unexpected error occurred' } }
  }
}

export const getCurrentUser = async () => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      console.error('Get user error:', error)
      return null
    }

    return user
  } catch (err) {
    console.error('Get user exception:', err)
    return null
  }
}

export const getSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      console.error('Get session error:', error)
      return null
    }

    return session
  } catch (err) {
    console.error('Get session exception:', err)
    return null
  }
}

// Auth state listener
export const onAuthStateChange = (callback) => {
  return supabase.auth.onAuthStateChange(callback)
}

// Storage helpers
export const uploadFile = async (file, filePath, options = {}) => {
  try {
    const { data, error } = await supabase.storage
      .from(storageBucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
        ...options
      })

    if (error) {
      console.error('File upload error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('File upload exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}

export const downloadFile = async (filePath) => {
  try {
    const { data, error } = await supabase.storage
      .from(storageBucket)
      .download(filePath)

    if (error) {
      console.error('File download error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('File download exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}

export const deleteFile = async (filePath) => {
  try {
    const { data, error } = await supabase.storage
      .from(storageBucket)
      .remove([filePath])

    if (error) {
      console.error('File delete error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('File delete exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}

export const getPublicUrl = (filePath) => {
  try {
    const { data } = supabase.storage
      .from(storageBucket)
      .getPublicUrl(filePath)

    return data?.publicUrl || ''
  } catch (err) {
    console.error('Get public URL exception:', err)
    return ''
  }
}

export const createSignedUrl = async (filePath, expiresIn = 3600) => {
  try {
    const { data, error } = await supabase.storage
      .from(storageBucket)
      .createSignedUrl(filePath, expiresIn)

    if (error) {
      console.error('Create signed URL error:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (err) {
    console.error('Create signed URL exception:', err)
    return { data: null, error: { message: 'An unexpected error occurred' } }
  }
}
