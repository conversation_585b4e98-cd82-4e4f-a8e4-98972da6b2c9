{"name": "doc-maker-frontend", "version": "0.1.0", "private": true, "dependencies": {"@supabase/supabase-js": "^2.38.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "react-scripts": "5.0.1", "axios": "^1.6.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "signature_pad": "^4.1.1"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}