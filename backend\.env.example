# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Your Supabase project URL (found in Project Settings > API)
SUPABASE_URL=https://your-project-id.supabase.co

# Service Role Key (NEVER use this in client-side code!)
# This key bypasses Row Level Security and should only be used server-side
# Found in Project Settings > API > service_role key
SUPABASE_KEY=your_supabase_service_role_key_here

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# Secret key for JWT token signing (minimum 32 characters recommended)
# Generate a secure key: openssl rand -hex 32
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production-min-32-chars

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Environment: development, staging, production
ENVIRONMENT=development

# Enable debug mode (true/false)
DEBUG=true

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# Supabase Storage bucket name for documents
STORAGE_BUCKET=documents

# Maximum file size in bytes (10MB = 10485760)
MAX_FILE_SIZE=10485760

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed origins for CORS (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# DATABASE CONFIGURATION (Optional)
# =============================================================================
# Direct PostgreSQL connection (if needed for advanced queries)
# Format: postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-ID].supabase.co:5432/postgres
# DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# For production deployments, also set these in your hosting platform:
# - Render: Environment Variables section
# - Railway: Variables tab
# - Heroku: Config Vars
# - Docker: Environment variables or .env file
