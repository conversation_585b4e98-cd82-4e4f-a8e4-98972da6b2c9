from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Dict, Any
from ..services.template_service import template_service
from ..routers.auth import get_current_user

router = APIRouter()

class TemplateResponse(BaseModel):
    id: str
    filename: str
    placeholders: List[Dict[str, str]]
    created_at: str = None

class PlaceholderResponse(BaseModel):
    id: str
    filename: str
    placeholders: List[Dict[str, str]]

@router.post("/upload", response_model=TemplateResponse)
async def upload_template(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_current_user)
):
    """Upload a .docx template and extract placeholders"""
    return await template_service.upload_template(file, current_user["id"])

@router.get("/", response_model=List[Dict[str, Any]])
async def get_templates(current_user: dict = Depends(get_current_user)):
    """Get all templates for the current user"""
    return await template_service.get_user_templates(current_user["id"])

@router.get("/{template_id}/placeholders", response_model=PlaceholderResponse)
async def get_template_placeholders(
    template_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get placeholders for a specific template"""
    return await template_service.get_template_placeholders(template_id, current_user["id"])
