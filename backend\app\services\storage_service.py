import os
import uuid
from typing import Optional, List, Dict, Any
from fastapi import UploadFile, HTTPException
import mimetypes
import logging
from pathlib import Path

from ..utils.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)

class StorageService:
    """Service for handling file storage operations with Supabase Storage"""

    def __init__(self):
        self.supabase = get_supabase_client()
        self.max_file_size = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB default
        self.allowed_extensions = {'.docx', '.doc', '.pdf', '.png', '.jpg', '.jpeg'}

    def _validate_file(self, file: UploadFile) -> None:
        """Validate uploaded file"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in self.allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
            )

        # Check file size (if available)
        if hasattr(file, 'size') and file.size and file.size > self.max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {self.max_file_size} bytes"
            )

    async def upload_template(self, file: UploadFile, user_id: str) -> Dict[str, Any]:
        """Upload a document template"""
        self._validate_file(file)

        try:
            # Generate unique filename
            file_ext = Path(file.filename).suffix.lower()
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            file_path = f"templates/{user_id}/{unique_filename}"

            # Read file content
            file_content = await file.read()

            # Determine content type
            content_type = mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

            # Upload to Supabase Storage
            result = await self.supabase.upload_file(file_path, file_content, content_type)

            return {
                "id": str(uuid.uuid4()),
                "original_filename": file.filename,
                "filename": unique_filename,
                "file_path": file_path,
                "url": result["url"],
                "size": len(file_content),
                "content_type": content_type
            }

        except Exception as e:
            logger.error(f"Template upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

    async def upload_document(self, file: UploadFile, user_id: str, document_id: str) -> Dict[str, Any]:
        """Upload a generated document"""
        try:
            # Generate filename
            file_ext = Path(file.filename).suffix.lower()
            filename = f"{document_id}{file_ext}"
            file_path = f"documents/{user_id}/{filename}"

            # Read file content
            file_content = await file.read()

            # Determine content type
            content_type = mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

            # Upload to Supabase Storage
            result = await self.supabase.upload_file(file_path, file_content, content_type)

            return {
                "filename": filename,
                "file_path": file_path,
                "url": result["url"],
                "size": len(file_content),
                "content_type": content_type
            }

        except Exception as e:
            logger.error(f"Document upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

    async def upload_signature(self, file: UploadFile, user_id: str) -> Dict[str, Any]:
        """Upload a signature image"""
        # Validate image file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")

        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in {'.png', '.jpg', '.jpeg'}:
            raise HTTPException(status_code=400, detail="Only PNG and JPEG images are allowed for signatures")

        try:
            # Generate unique filename
            unique_filename = f"{uuid.uuid4()}{file_ext}"
            file_path = f"signatures/{user_id}/{unique_filename}"

            # Read file content
            file_content = await file.read()

            # Upload to Supabase Storage
            result = await self.supabase.upload_file(file_path, file_content, f"image/{file_ext[1:]}")

            return {
                "filename": unique_filename,
                "file_path": file_path,
                "url": result["url"],
                "size": len(file_content)
            }

        except Exception as e:
            logger.error(f"Signature upload error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

    async def download_file(self, file_path: str, user_id: str) -> bytes:
        """Download a file (with user access validation)"""
        # Basic security check - ensure user can only access their own files
        if not (file_path.startswith(f"templates/{user_id}/") or
                file_path.startswith(f"documents/{user_id}/") or
                file_path.startswith(f"signatures/{user_id}/")):
            raise HTTPException(status_code=403, detail="Access denied")

        try:
            return await self.supabase.download_file(file_path)
        except Exception as e:
            logger.error(f"File download error: {str(e)}")
            raise HTTPException(status_code=404, detail="File not found")

    async def delete_file(self, file_path: str, user_id: str) -> bool:
        """Delete a file (with user access validation)"""
        # Basic security check
        if not (file_path.startswith(f"templates/{user_id}/") or
                file_path.startswith(f"documents/{user_id}/") or
                file_path.startswith(f"signatures/{user_id}/")):
            raise HTTPException(status_code=403, detail="Access denied")

        try:
            return await self.supabase.delete_file(file_path)
        except Exception as e:
            logger.error(f"File deletion error: {str(e)}")
            return False

    async def get_signed_url(self, file_path: str, user_id: str, expires_in: int = 3600) -> str:
        """Get a signed URL for private file access"""
        # Basic security check
        if not (file_path.startswith(f"templates/{user_id}/") or
                file_path.startswith(f"documents/{user_id}/") or
                file_path.startswith(f"signatures/{user_id}/")):
            raise HTTPException(status_code=403, detail="Access denied")

        try:
            return await self.supabase.create_signed_url(file_path, expires_in)
        except Exception as e:
            logger.error(f"Signed URL error: {str(e)}")
            return ""

# Global instance
storage_service = StorageService()
