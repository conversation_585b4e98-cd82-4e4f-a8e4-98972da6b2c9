from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTP<PERSON>xception, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional
from ..services.auth_service import auth_service

router = APIRouter()
security = HTTPBearer()

class UserRegister(BaseModel):
    email: EmailStr
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    id: str
    email: str

class AuthResponse(BaseModel):
    user: UserResponse
    session: dict

async def get_current_user(token: HTTPAuthorizationCredentials = Depends(security)) -> dict:
    """Dependency to get current authenticated user"""
    return await auth_service.authenticate_user(token.credentials)

@router.post("/register", response_model=AuthResponse)
async def register(user_data: UserRegister):
    """Register a new user"""
    return await auth_service.register_user(user_data.email, user_data.password)

@router.post("/login", response_model=AuthResponse)
async def login(user_data: UserLogin):
    """Login user"""
    return await auth_service.login_user(user_data.email, user_data.password)

@router.get("/me", response_model=UserResponse)
async def get_me(current_user: dict = Depends(get_current_user)):
    """Get current user information"""
    return UserResponse(
        id=current_user["id"],
        email=current_user["email"]
    )
